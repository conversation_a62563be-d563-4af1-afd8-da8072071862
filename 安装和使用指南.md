# 施工进度甘特图管理系统 - 安装和使用指南

## 功能特点

✅ **完整的任务管理功能**
- 添加、编辑、删除任务
- 任务分组管理
- 任务时间设置（开始时间、持续时间）
- 任务颜色自定义

✅ **任务操作功能**
- 任务选择（单选、多选、批量操作）
- 任务分组筛选
- 任务自定义显示
- 任务上下移动和复制

✅ **甘特图显示功能**
- 可视化甘特图展示
- 时间标尺（支持日/周/月视图）
- 任务条拖拽调整（左右移动、调整长度）
- 任务条样式自定义

✅ **界面设置功能**
- 列显示设置（可选择显示/隐藏特定列）
- 列宽调整（可拖拽调整列宽）
- 面板宽度调整

✅ **数据管理功能**
- 本地存储（使用localStorage保存数据）
- Excel导入导出功能
- 数据持久化

✅ **高级功能**
- 响应式设计（支持桌面、平板、手机）
- 任务拖拽移动和调整大小
- 任务条样式自定义（高度、透明度、边框、圆角）
- 打印支持

## 安装步骤

### 1. 环境要求
- Node.js (版本 14 或更高)
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 安装依赖
在项目目录中运行以下命令：

```bash
npm install
```

### 3. 启动应用

**开发模式：**
```bash
npm run dev
```

**生产模式：**
```bash
npm start
```

### 4. 访问应用
打开浏览器并访问：`http://localhost:3000`

## 使用说明

### 基本操作

#### 添加任务
1. 在左侧面板的"任务管理"区域填写任务信息
2. 选择任务分组、开始日期、持续时间和颜色
3. 点击"添加任务"按钮

#### 编辑任务
1. 在任务列表中点击要编辑的任务
2. 修改表单中的任务信息
3. 点击"更新任务"按钮

#### 删除任务
1. 在任务列表中点击任务右侧的删除按钮
2. 确认删除操作

### 任务分组和批量操作

#### 任务分组筛选
- 使用"任务分组筛选"下拉框筛选特定分组的任务

#### 批量选择任务
- 勾选任务列表中的复选框
- 可以同时选择多个任务

#### 批量分组
1. 选择多个任务
2. 点击工具栏中的"任务分组"按钮
3. 输入新的分组名称

#### 批量删除
1. 选择多个任务
2. 点击工具栏中的"删除选中"按钮
3. 确认删除操作

### 甘特图操作

#### 任务条拖拽移动
- 直接拖拽甘特图中的任务条来调整开始时间

#### 任务条调整大小
- 拖拽任务条左右两侧的调整手柄来改变持续时间
- 拖拽左侧手柄可以同时调整开始时间和持续时间
- 拖拽右侧手柄只调整持续时间

#### 时间刻度切换
- 使用右上角的"日视图/周视图/月视图"切换时间刻度

### 样式自定义

#### 任务条样式设置
1. 点击"图表设置"按钮
2. 调整任务条高度、透明度、边框等参数
3. 点击"应用样式"按钮

#### 列显示设置
1. 点击"列设置"按钮
2. 选择要显示的列
3. 调整任务标签宽度和左侧面板宽度
4. 点击"应用设置"按钮

### 数据管理

#### 本地存储
- 点击"保存到本地"将数据保存到浏览器本地存储
- 点击"从本地加载"从浏览器本地存储恢复数据

#### Excel导入导出
- 点击"导入Excel"按钮从Excel文件导入任务数据
- 点击"导出Excel"按钮将任务数据导出为Excel文件

#### 项目保存
- 点击工具栏中的"保存项目"按钮保存当前项目

### 响应式设计

系统支持多种设备尺寸：
- **桌面设备**：完整功能显示
- **平板设备**：优化的触控界面
- **手机设备**：紧凑布局，支持触摸操作

### 打印功能

- 使用浏览器的打印功能（Ctrl+P）
- 系统会自动优化打印布局
- 只打印甘特图部分，隐藏控制面板

## 技术架构

### 前端技术
- HTML5
- CSS3（包含Flexbox、Grid、响应式设计）
- JavaScript (原生ES6+)
- Font Awesome (图标库)
- SheetJS (Excel处理)

### 后端技术
- Node.js
- Express.js
- 中间件：cors、morgan

### 数据存储
- 浏览器 localStorage（前端）
- 可扩展支持数据库（后端）

## 文件结构

```
D:\DP\横道图\
├── index.html              # 主应用文件
├── package.json           # 项目配置文件
├── server.js              # Node.js服务器
├── readme.txt             # 项目说明文档
└── 安装和使用指南.md      # 本文档
```

## 故障排除

### 常见问题

#### 1. 应用无法启动
- 确保已安装Node.js
- 运行 `npm install` 安装依赖
- 检查端口3000是否被占用

#### 2. 甘特图显示异常
- 刷新页面重置应用状态
- 清除浏览器缓存
- 检查浏览器是否支持现代JavaScript特性

#### 3. 数据丢失
- 确保已点击"保存到本地"按钮
- 检查浏览器localStorage是否启用
- 避免使用隐身模式

#### 4. Excel导入失败
- 确保Excel文件格式正确（.xlsx或.xls）
- 检查文件是否包含必要的列（任务名称、开始日期、持续时间等）
- 确保文件没有损坏

## 开发说明

### 代码结构
- **HTML结构**：使用语义化标签，清晰的层次结构
- **CSS样式**：采用BEM命名规范，响应式设计
- **JavaScript**：模块化设计，事件驱动架构

### 扩展功能
系统设计支持以下扩展：
- 数据库集成（MySQL、MongoDB等）
- 用户认证和权限管理
- 多项目支持
- 实时协作功能
- 更多导出格式（PDF、图片等）

## 联系方式

如有问题或建议，请联系开发团队。

---

## 版本信息

- **当前版本**：1.0.0
- **发布日期**：2023年
- **最后更新**：最近

感谢使用施工进度甘特图管理系统！