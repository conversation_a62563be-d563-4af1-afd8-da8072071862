const express = require('express');
const path = require('path');
const cors = require('cors');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(cors());
app.use(morgan('dev'));
app.use(express.json());
app.use(express.static(path.join(__dirname, '.')));

// 路由配置
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// API 路由 - 获取任务数据
app.get('/api/tasks', (req, res) => {
    // 这里可以从数据库获取数据，目前返回空数组
    res.json([]);
});

// API 路由 - 保存任务数据
app.post('/api/tasks', (req, res) => {
    const tasks = req.body;
    // 这里可以保存数据到数据库
    console.log('保存任务数据:', tasks);
    res.json({ success: true, message: '任务数据已保存' });
});

// API 路由 - 导出Excel
app.post('/api/export', (req, res) => {
    const tasks = req.body;
    // 这里可以处理Excel导出逻辑
    res.json({ success: true, message: 'Excel导出成功', data: tasks });
});

// 404错误处理
app.use((req, res) => {
    res.status(404).json({ message: '页面未找到' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: '服务器内部错误' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`甘特图管理系统服务器运行在 http://localhost:${PORT}`);
    console.log('请在浏览器中访问上述地址以使用系统');
});

module.exports = app;