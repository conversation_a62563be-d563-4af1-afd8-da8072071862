// 横道图系统修改验证脚本
// 在浏览器控制台中运行此脚本来验证修改是否正确

function validateModifications() {
    console.log('🔍 开始验证横道图系统修改...\n');
    
    let passedTests = 0;
    let totalTests = 0;
    
    // 测试1: 检查左侧面板结构
    totalTests++;
    console.log('测试1: 检查左侧面板结构');
    const panelLeft = document.querySelector('.panel-left');
    const quickActions = document.querySelector('.quick-actions');
    const addTaskBtn = document.querySelector('#addTaskBtn');
    const addGroupBtn = document.querySelector('#addGroupBtn');
    
    if (panelLeft && quickActions && addTaskBtn && addGroupBtn) {
        console.log('✅ 左侧面板结构正确');
        passedTests++;
    } else {
        console.log('❌ 左侧面板结构不正确');
    }
    
    // 测试2: 检查甘特图列标题
    totalTests++;
    console.log('\n测试2: 检查甘特图列标题');
    const timelineHeaderColumns = document.querySelector('.timeline-header-columns');
    const columnHeaders = document.querySelectorAll('.column-header');
    
    if (timelineHeaderColumns && columnHeaders.length === 6) {
        const expectedHeaders = ['分组名', '编号', '任务名称', '持续时间', '开始时间', '结束时间'];
        let headersCorrect = true;
        
        columnHeaders.forEach((header, index) => {
            if (header.textContent.trim() !== expectedHeaders[index]) {
                headersCorrect = false;
            }
        });
        
        if (headersCorrect) {
            console.log('✅ 甘特图列标题正确');
            passedTests++;
        } else {
            console.log('❌ 甘特图列标题不正确');
        }
    } else {
        console.log('❌ 甘特图列标题结构不正确');
    }
    
    // 测试3: 检查任务行结构
    totalTests++;
    console.log('\n测试3: 检查任务行结构');
    const taskRows = document.querySelectorAll('.task-row');
    const taskColumns = document.querySelectorAll('.task-columns');
    
    if (taskRows.length > 0 && taskColumns.length > 0) {
        console.log('✅ 任务行结构正确');
        passedTests++;
    } else {
        console.log('❌ 任务行结构不正确');
    }
    
    // 测试4: 检查是否删除了不需要的元素
    totalTests++;
    console.log('\n测试4: 检查是否删除了不需要的元素');
    const taskForm = document.querySelector('.task-form');
    const taskList = document.querySelector('.task-list');
    const dataActions = document.querySelector('.data-actions');
    
    if (!taskForm && !taskList && !dataActions) {
        console.log('✅ 已正确删除不需要的元素');
        passedTests++;
    } else {
        console.log('❌ 仍存在应该删除的元素');
    }
    
    // 测试5: 检查CSS样式
    totalTests++;
    console.log('\n测试5: 检查CSS样式');
    const panelLeftStyle = window.getComputedStyle(panelLeft);
    const btnLarge = document.querySelector('.btn-large');
    
    if (btnLarge && panelLeftStyle.flexBasis === '200px') {
        console.log('✅ CSS样式正确');
        passedTests++;
    } else {
        console.log('❌ CSS样式不正确');
    }
    
    // 输出测试结果
    console.log('\n📊 测试结果总结:');
    console.log(`通过测试: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！修改成功完成。');
    } else {
        console.log('⚠️ 部分测试未通过，请检查修改。');
    }
    
    return {
        passed: passedTests,
        total: totalTests,
        success: passedTests === totalTests
    };
}

// 检查页面元素的详细信息
function inspectElements() {
    console.log('🔍 检查页面元素详细信息:\n');
    
    // 检查左侧面板
    const panelLeft = document.querySelector('.panel-left');
    if (panelLeft) {
        console.log('左侧面板:', panelLeft);
        console.log('左侧面板子元素:', panelLeft.children);
    }
    
    // 检查甘特图标题
    const timelineHeaderColumns = document.querySelector('.timeline-header-columns');
    if (timelineHeaderColumns) {
        console.log('甘特图标题容器:', timelineHeaderColumns);
        console.log('列标题:', Array.from(timelineHeaderColumns.children).map(el => el.textContent));
    }
    
    // 检查任务行
    const taskRows = document.querySelectorAll('.task-row');
    console.log('任务行数量:', taskRows.length);
    
    if (taskRows.length > 0) {
        console.log('第一个任务行的列数据:');
        const firstRowColumns = taskRows[0].querySelectorAll('.task-column');
        Array.from(firstRowColumns).forEach((col, index) => {
            console.log(`  列${index + 1}: ${col.textContent}`);
        });
    }
}

// 自动运行验证
console.log('横道图系统修改验证脚本已加载');
console.log('运行 validateModifications() 来验证修改');
console.log('运行 inspectElements() 来检查元素详情');

// 如果页面已加载，自动运行验证
if (document.readyState === 'complete') {
    setTimeout(validateModifications, 1000);
} else {
    window.addEventListener('load', () => {
        setTimeout(validateModifications, 1000);
    });
}
