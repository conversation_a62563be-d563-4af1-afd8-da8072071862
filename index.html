<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>施工进度甘特图管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --border-radius: 6px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        /* 顶部标题栏 */
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
            z-index: 100;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo i {
            font-size: 2.5rem;
            color: var(--secondary-color);
        }
        
        .logo h1 {
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .logo span {
            color: var(--secondary-color);
        }
        
        /* 主内容区域 */
        .main-content {
            display: flex;
            flex: 1;
            padding: 20px;
            gap: 20px;
        }
        
        /* 左侧面板 - 任务管理 */
        .panel-left {
            flex: 0 0 380px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            display: none;
        }
        
        .panel-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .task-form {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--dark-color);
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        
        .color-picker {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .color-option.active {
            border-color: var(--dark-color);
        }
        
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-danger {
            background-color: var(--accent-color);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-success:hover {
            background-color: #219653;
        }
        
        .btn-warning {
            background-color: var(--warning-color);
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #e67e22;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-group .btn {
            flex: 1;
        }
        
        /* 任务列表 */
        .task-list {
            flex: 1;
            overflow-y: auto;
        }
        
        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .task-checkbox {
            margin-right: 10px;
        }
        
        .task-checkbox input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .task-item:hover {
            background-color: #f9f9f9;
        }
        
        .task-item.selected {
            background-color: #e3f2fd;
        }
        
        .task-color {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .task-info {
            flex: 1;
        }
        
        .task-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .task-dates {
            font-size: 0.85rem;
            color: #666;
        }
        
        .task-actions {
            display: flex;
            gap: 10px;
        }
        
        .task-actions button {
            background: none;
            border: none;
            color: #777;
            cursor: pointer;
            font-size: 1rem;
            transition: color 0.2s;
        }
        
        .task-actions button:hover {
            color: var(--secondary-color);
        }
        
        /* 甘特图区域 */
        .panel-right {
            flex: 1;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .gantt-header {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background-color: var(--light-color);
            border-bottom: 1px solid #ddd;
        }
        
        .gantt-controls {
            display: flex;
            gap: 10px;
        }
        
        .gantt-container {
            flex: 1;
            overflow: auto;
            position: relative;
        }
        
        .timeline {
            height: 60px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: flex;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .timeline-header {
            min-width: 200px;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            padding: 0 15px;
        }
        
        .timeline-months {
            display: flex;
            flex: 1;
        }
        
        .timeline-month {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #eee;
            min-width: 150px;
        }
        
        .month-name {
            font-weight: 600;
            padding: 5px;
        }
        
        .month-days {
            display: flex;
            width: 100%;
        }
        
        .month-day {
            flex: 1;
            text-align: center;
            padding: 3px;
            font-size: 0.8rem;
            border-top: 1px solid #eee;
        }
        
        .gantt-chart {
            position: relative;
            height: calc(100% - 60px);
        }
        
        .task-row {
            height: 50px;
            display: flex;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        
        .task-label {
            min-width: 200px;
            display: flex;
            align-items: center;
            padding: 0 15px;
            background-color: white;
            position: sticky;
            left: 0;
            z-index: 2;
            border-right: 1px solid #eee;
        }
        
        .task-bar-container {
            flex: 1;
            position: relative;
        }
        
        .task-bar {
            height: 30px;
            background-color: var(--secondary-color);
            border-radius: 4px;
            position: absolute;
            top: 10px;
            cursor: move;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-size: 0.85rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: opacity 0.2s;
            user-select: none;
        }
        
        .task-bar:hover {
            opacity: 0.9;
        }
        
        .task-bar.dragging {
            opacity: 0.7;
            z-index: 1000;
        }
        
        .resize-handle {
            position: absolute;
            top: 0;
            width: 8px;
            height: 100%;
            cursor: ew-resize;
            background-color: rgba(255,255,255,0.3);
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .resize-handle:hover,
        .task-bar:hover .resize-handle {
            opacity: 1;
        }
        
        .resize-handle.left {
            left: 0;
            border-radius: 4px 0 0 4px;
        }
        
        .resize-handle.right {
            right: 0;
            border-radius: 0 4px 4px 0;
        }
        
        /* 工具栏 */
        .toolbar {
            padding: 15px 20px;
            background-color: white;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .toolbar-group {
            display: flex;
            gap: 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }
            
            .panel-left {
                flex: 0 0 auto;
                max-height: 400px;
            }
        }
        
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 10px;
            }
            
            .toolbar-group {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .toolbar-group .btn {
                flex: 1;
                min-width: 120px;
            }
            
            .gantt-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .gantt-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .timeline {
                height: 80px;
            }
            
            .timeline-header {
                min-width: 150px;
                font-size: 0.9rem;
                padding: 0 10px;
            }
            
            .timeline-month {
                min-width: 120px;
            }
            
            .month-name {
                font-size: 0.8rem;
            }
            
            .month-day {
                font-size: 0.7rem;
            }
            
            .task-label {
                min-width: 150px !important;
                font-size: 0.9rem;
                padding: 0 10px;
            }
            
            .task-bar {
                font-size: 0.7rem;
                padding: 0 5px;
            }
            
            .task-row {
                height: 40px;
            }
            
            .panel-left {
                flex: 0 0 100%;
            }
            
            .task-form {
                padding: 15px;
            }
            
            .form-group {
                margin-bottom: 10px;
            }
            
            .btn {
                padding: 8px 12px;
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.2rem;
            }
            
            .logo i {
                font-size: 2rem;
            }
            
            .toolbar-group .btn {
                min-width: 100px;
                font-size: 0.8rem;
                padding: 6px 10px;
            }
            
            .gantt-container {
                overflow-x: auto;
                overflow-y: auto;
            }
            
            .task-list {
                font-size: 0.9rem;
            }
            
            .task-item {
                padding: 10px 15px;
            }
            
            .task-name {
                font-size: 0.9rem;
            }
            
            .task-dates {
                font-size: 0.8rem;
            }
            
            .task-actions button {
                font-size: 0.9rem;
                padding: 5px;
            }
            
            .modal-content {
                width: 95%;
                margin: 10px;
            }
            
            .modal-header, .modal-body, .modal-footer {
                padding: 10px 15px;
            }
        }
        
        /* 打印样式 */
        @media print {
            .header,
            .toolbar,
            .panel-left,
            .btn,
            .modal,
            .status-message,
            .loader {
                display: none !important;
            }
            
            .main-content {
                display: block !important;
            }
            
            .panel-right {
                display: block !important;
                box-shadow: none !important;
                border: none !important;
            }
            
            .gantt-container {
                overflow: visible !important;
            }
            
            .task-bar {
                page-break-inside: avoid;
            }
        }
        
        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 15px 20px;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            background-color: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .close {
            cursor: pointer;
            font-size: 1.5rem;
        }
        
        /* 状态消息 */
        .status-message {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: var(--border-radius);
            background-color: var(--success-color);
            color: white;
            box-shadow: var(--shadow);
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .status-message.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        /* 导入导出区域 */
        .data-actions {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        /* 加载指示器 */
        .loader {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: 20px;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(0,0,0,0.1);
            border-radius: 50%;
            border-top-color: var(--secondary-color);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-project-diagram"></i>
                <h1>施工进度<span>甘特图</span>管理系统</h1>
            </div>
            <div class="user-info">
                <i class="fas fa-user-circle"></i> 管理员
            </div>
        </header>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-group">
                <button class="btn btn-primary" id="newProjectBtn">
                    <i class="fas fa-plus"></i> 新建项目
                </button>
                <button class="btn btn-success" id="saveProjectBtn">
                    <i class="fas fa-save"></i> 保存项目
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="btn btn-warning" id="importBtn">
                    <i class="fas fa-file-import"></i> 导入Excel
                </button>
                <button class="btn btn-success" id="exportBtn">
                    <i class="fas fa-file-export"></i> 导出Excel
                </button>
            </div>
            
            <div class="toolbar-group">
                <button class="btn btn-primary" id="groupTasksBtn">
                    <i class="fas fa-object-group"></i> 任务分组
                </button>
                <button class="btn btn-danger" id="deleteTasksBtn">
                    <i class="fas fa-trash"></i> 删除选中
                </button>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 左侧面板 - 任务管理 -->
            <div class="panel-left">
                <div class="panel-header">
                    <i class="fas fa-tasks"></i>
                    <span>任务管理</span>
                </div>
                
                <div class="task-form">
                    <div class="form-group">
                        <label for="taskName">任务名称</label>
                        <input type="text" id="taskName" placeholder="输入任务名称">
                    </div>
                    
                    <div class="form-group">
                        <label for="taskGroup">任务分组</label>
                        <select id="taskGroup">
                            <option value="基础施工">基础施工</option>
                            <option value="主体结构">主体结构</option>
                            <option value="水电安装">水电安装</option>
                            <option value="装饰装修">装饰装修</option>
                            <option value="室外工程">室外工程</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="taskStartDate">开始日期</label>
                        <input type="date" id="taskStartDate">
                    </div>
                    
                    <div class="form-group">
                        <label for="taskDuration">持续时间 (天)</label>
                        <input type="number" id="taskDuration" min="1" value="7">
                    </div>
                    
                    <div class="form-group">
                        <label>任务颜色</label>
                        <div class="color-picker">
                            <div class="color-option active" style="background-color: #3498db;" data-color="#3498db"></div>
                            <div class="color-option" style="background-color: #e74c3c;" data-color="#e74c3c"></div>
                            <div class="color-option" style="background-color: #2ecc71;" data-color="#2ecc71"></div>
                            <div class="color-option" style="background-color: #f39c12;" data-color="#f39c12"></div>
                            <div class="color-option" style="background-color: #9b59b6;" data-color="#9b59b6"></div>
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="addTaskBtn">
                            <i class="fas fa-plus"></i> 添加任务
                        </button>
                        <button class="btn btn-warning" id="updateTaskBtn" disabled>
                            <i class="fas fa-sync"></i> 更新任务
                        </button>
                    </div>
                </div>
                
                <!-- 任务筛选器 -->
                <div style="padding: 10px 20px; border-bottom: 1px solid #eee; background-color: #f8f9fa;">
                    <label for="taskGroupFilter" style="font-weight: 500; margin-bottom: 5px; display: block;">任务分组筛选</label>
                    <select id="taskGroupFilter" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="all">所有分组</option>
                        <option value="基础施工">基础施工</option>
                        <option value="主体结构">主体结构</option>
                        <option value="水电安装">水电安装</option>
                        <option value="装饰装修">装饰装修</option>
                        <option value="室外工程">室外工程</option>
                    </select>
                </div>
                
                <!-- 任务列表 -->
                <div class="task-list" id="taskList">
                    <div class="task-item selected">
                        <div class="task-color" style="background-color: #3498db;"></div>
                        <div class="task-info">
                            <div class="task-name">场地平整</div>
                            <div class="task-dates">2023-08-01 至 2023-08-07 (7天)</div>
                        </div>
                        <div class="task-actions">
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    
                    <div class="task-item">
                        <div class="task-color" style="background-color: #2ecc71;"></div>
                        <div class="task-info">
                            <div class="task-name">基坑开挖</div>
                            <div class="task-dates">2023-08-08 至 2023-08-15 (8天)</div>
                        </div>
                        <div class="task-actions">
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    
                    <div class="task-item">
                        <div class="task-color" style="background-color: #f39c12;"></div>
                        <div class="task-info">
                            <div class="task-name">基础垫层</div>
                            <div class="task-dates">2023-08-16 至 2023-08-20 (5天)</div>
                        </div>
                        <div class="task-actions">
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    
                    <div class="task-item">
                        <div class="task-color" style="background-color: #9b59b6;"></div>
                        <div class="task-info">
                            <div class="task-name">基础钢筋绑扎</div>
                            <div class="task-dates">2023-08-21 至 2023-08-28 (8天)</div>
                        </div>
                        <div class="task-actions">
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    
                    <div class="task-item">
                        <div class="task-color" style="background-color: #e74c3c;"></div>
                        <div class="task-info">
                            <div class="task-name">基础混凝土浇筑</div>
                            <div class="task-dates">2023-08-29 至 2023-09-05 (8天)</div>
                        </div>
                        <div class="task-actions">
                            <button><i class="fas fa-edit"></i></button>
                            <button><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                </div>
                
                <!-- 数据管理 -->
                <div class="data-actions">
                    <h3><i class="fas fa-database"></i> 数据管理</h3>
                    <div class="action-buttons">
                        <button class="btn btn-primary" id="saveLocalBtn">
                            <i class="fas fa-hdd"></i> 保存到本地
                        </button>
                        <button class="btn btn-warning" id="loadLocalBtn">
                            <i class="fas fa-folder-open"></i> 从本地加载
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右侧面板 - 甘特图 -->
            <div class="panel-right">
                <div class="panel-header">
                    <i class="fas fa-chart-bar"></i>
                    <span>施工进度甘特图</span>
                </div>
                
                <div class="gantt-header">
                    <div class="gantt-title">
                        <i class="fas fa-hard-hat"></i> 住宅楼建设项目
                    </div>
                    <div class="gantt-controls">
                        <select id="timeScale">
                            <option value="day">日视图</option>
                            <option value="week">周视图</option>
                            <option value="month" selected>月视图</option>
                        </select>
                        <button class="btn btn-primary" id="columnSettingsBtn">
                            <i class="fas fa-columns"></i> 列设置
                        </button>
                        <button class="btn btn-primary" id="chartSettingsBtn">
                            <i class="fas fa-cog"></i> 图表设置
                        </button>
                    </div>
                </div>
                
                <div class="gantt-container">
                    <div class="timeline">
                        <div class="timeline-header">任务/日期</div>
                        <div class="timeline-months">
                            <div class="timeline-month">
                                <div class="month-name">2023年8月</div>
                                <div class="month-days">
                                    <div class="month-day">1</div>
                                    <div class="month-day">2</div>
                                    <div class="month-day">3</div>
                                    <!-- 完整日期省略 -->
                                </div>
                            </div>
                            <div class="timeline-month">
                                <div class="month-name">2023年9月</div>
                                <div class="month-days">
                                    <div class="month-day">1</div>
                                    <div class="month-day">2</div>
                                    <div class="month-day">3</div>
                                    <!-- 完整日期省略 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="gantt-chart">
                        <div class="task-row">
                            <div class="task-label">场地平整</div>
                            <div class="task-bar-container">
                                <div class="task-bar" style="background-color: #3498db; left: 0%; width: 22.5%;">场地平整</div>
                            </div>
                        </div>
                        
                        <div class="task-row">
                            <div class="task-label">基坑开挖</div>
                            <div class="task-bar-container">
                                <div class="task-bar" style="background-color: #2ecc71; left: 22.5%; width: 25%;">基坑开挖</div>
                            </div>
                        </div>
                        
                        <div class="task-row">
                            <div class="task-label">基础垫层</div>
                            <div class="task-bar-container">
                                <div class="task-bar" style="background-color: #f39c12; left: 47.5%; width: 15%;">基础垫层</div>
                            </div>
                        </div>
                        
                        <div class="task-row">
                            <div class="task-label">基础钢筋绑扎</div>
                            <div class="task-bar-container">
                                <div class="task-bar" style="background-color: #9b59b6; left: 62.5%; width: 25%;">基础钢筋绑扎</div>
                            </div>
                        </div>
                        
                        <div class="task-row">
                            <div class="task-label">基础混凝土浇筑</div>
                            <div class="task-bar-container">
                                <div class="task-bar" style="background-color: #e74c3c; left: 87.5%; width: 25%;">基础混凝土浇筑</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div class="modal" id="importModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-file-import"></i> 从Excel导入数据</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p>请选择Excel文件进行导入。系统将自动解析任务数据。</p>
                <div class="form-group">
                    <label for="excelFile">选择Excel文件</label>
                    <input type="file" id="excelFile" accept=".xlsx, .xls">
                </div>
                <div class="form-group">
                    <label>导入选项</label>
                    <div>
                        <input type="checkbox" id="replaceData" checked>
                        <label for="replaceData">替换现有数据</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-warning" id="cancelImportBtn">取消</button>
                <button class="btn btn-success" id="confirmImportBtn">导入数据</button>
            </div>
        </div>
    </div>
    
    <!-- 样式设置模态框 -->
    <div class="modal" id="styleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-palette"></i> 任务条样式设置</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="taskBarHeight">任务条高度</label>
                    <input type="range" id="taskBarHeight" min="20" max="50" value="30">
                    <span id="taskBarHeightValue">30px</span>
                </div>
                
                <div class="form-group">
                    <label for="taskBarOpacity">透明度</label>
                    <input type="range" id="taskBarOpacity" min="0.3" max="1" step="0.1" value="1">
                    <span id="taskBarOpacityValue">100%</span>
                </div>
                
                <div class="form-group">
                    <label for="taskBarBorderWidth">边框宽度</label>
                    <input type="range" id="taskBarBorderWidth" min="0" max="5" value="0">
                    <span id="taskBarBorderWidthValue">0px</span>
                </div>
                
                <div class="form-group">
                    <label for="taskBarBorderColor">边框颜色</label>
                    <input type="color" id="taskBarBorderColor" value="#000000">
                </div>
                
                <div class="form-group">
                    <label for="taskBarBorderRadius">圆角大小</label>
                    <input type="range" id="taskBarBorderRadius" min="0" max="10" value="4">
                    <span id="taskBarBorderRadiusValue">4px</span>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-warning" id="cancelStyleBtn">取消</button>
                <button class="btn btn-success" id="applyStyleBtn">应用样式</button>
            </div>
        </div>
    </div>
    
    <!-- 列设置模态框 -->
    <div class="modal" id="columnModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-columns"></i> 列显示设置</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>可见列</label>
                    <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="showCheckbox" checked>
                            <span>选择框</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="showColor" checked>
                            <span>任务颜色</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="showName" checked>
                            <span>任务名称</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="showDates" checked>
                            <span>时间信息</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="showActions" checked>
                            <span>操作按钮</span>
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="taskLabelWidth">任务标签宽度</label>
                    <input type="range" id="taskLabelWidth" min="100" max="400" value="200">
                    <span id="taskLabelWidthValue">200px</span>
                </div>
                
                <div class="form-group">
                    <label for="leftPanelWidth">左侧面板宽度</label>
                    <input type="range" id="leftPanelWidth" min="300" max="600" value="380">
                    <span id="leftPanelWidthValue">380px</span>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-warning" id="cancelColumnBtn">取消</button>
                <button class="btn btn-success" id="applyColumnBtn">应用设置</button>
            </div>
        </div>
    </div>
    
    <!-- 状态消息 -->
    <div class="status-message" id="statusMessage">
        <i class="fas fa-check-circle"></i> <span>项目已成功保存！</span>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loader" id="loader">
        <div class="spinner"></div>
        <div>正在处理数据，请稍候...</div>
    </div>
    
    <script>
        // 示例任务数据
        const tasks = [
            { id: 1, name: "场地平整", group: "基础施工", start: "2023-08-01", duration: 7, color: "#3498db" },
            { id: 2, name: "基坑开挖", group: "基础施工", start: "2023-08-08", duration: 8, color: "#2ecc71" },
            { id: 3, name: "基础垫层", group: "基础施工", start: "2023-08-16", duration: 5, color: "#f39c12" },
            { id: 4, name: "基础钢筋绑扎", group: "主体结构", start: "2023-08-21", duration: 8, color: "#9b59b6" },
            { id: 5, name: "基础混凝土浇筑", group: "主体结构", start: "2023-08-29", duration: 8, color: "#e74c3c" }
        ];
        
        // 当前选中的任务ID
        let selectedTaskId = 1;
        let selectedTasks = new Set();
        let currentFilter = 'all';
        
        // 拖放相关变量
        let isDragging = false;
        let isResizing = false;
        let currentDragElement = null;
        let dragStartX = 0;
        let dragStartLeft = 0;
        let dragStartWidth = 0;
        let currentTaskId = null;
        
        // DOM元素
        const taskList = document.getElementById('taskList');
        const addTaskBtn = document.getElementById('addTaskBtn');
        const updateTaskBtn = document.getElementById('updateTaskBtn');
        const saveProjectBtn = document.getElementById('saveProjectBtn');
        const importBtn = document.getElementById('importBtn');
        const exportBtn = document.getElementById('exportBtn');
        const importModal = document.getElementById('importModal');
        const statusMessage = document.getElementById('statusMessage');
        const loader = document.getElementById('loader');
        const groupTasksBtn = document.getElementById('groupTasksBtn');
        const deleteTasksBtn = document.getElementById('deleteTasksBtn');
        const taskGroupFilter = document.getElementById('taskGroupFilter');
        const chartSettingsBtn = document.getElementById('chartSettingsBtn');
        const columnSettingsBtn = document.getElementById('columnSettingsBtn');
        const styleModal = document.getElementById('styleModal');
        const columnModal = document.getElementById('columnModal');
        
        // 样式设置相关元素
        const taskBarHeight = document.getElementById('taskBarHeight');
        const taskBarOpacity = document.getElementById('taskBarOpacity');
        const taskBarBorderWidth = document.getElementById('taskBarBorderWidth');
        const taskBarBorderColor = document.getElementById('taskBarBorderColor');
        const taskBarBorderRadius = document.getElementById('taskBarBorderRadius');
        
        // 样式设置显示值元素
        const taskBarHeightValue = document.getElementById('taskBarHeightValue');
        const taskBarOpacityValue = document.getElementById('taskBarOpacityValue');
        const taskBarBorderWidthValue = document.getElementById('taskBarBorderWidthValue');
        const taskBarBorderRadiusValue = document.getElementById('taskBarBorderRadiusValue');
        
        // 列设置相关元素
        const showCheckbox = document.getElementById('showCheckbox');
        const showColor = document.getElementById('showColor');
        const showName = document.getElementById('showName');
        const showDates = document.getElementById('showDates');
        const showActions = document.getElementById('showActions');
        const taskLabelWidth = document.getElementById('taskLabelWidth');
        const leftPanelWidth = document.getElementById('leftPanelWidth');
        const taskLabelWidthValue = document.getElementById('taskLabelWidthValue');
        const leftPanelWidthValue = document.getElementById('leftPanelWidthValue');
        
        // 初始化颜色选择器
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                this.classList.add('active');
            });
        });
        
        // 任务分组筛选
        taskGroupFilter.addEventListener('change', function() {
            currentFilter = this.value;
            renderTaskList();
        });
        
        // 任务分组按钮
        groupTasksBtn.addEventListener('click', function() {
            if (selectedTasks.size === 0) {
                showStatusMessage('请先选择要分组的任务', 'error');
                return;
            }
            
            const newGroup = prompt('请输入新的分组名称：');
            if (newGroup && newGroup.trim()) {
                const groupName = newGroup.trim();
                selectedTasks.forEach(taskId => {
                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.group = groupName;
                    }
                });
                
                selectedTasks.clear();
                renderTaskList();
                renderGanttChart();
                showStatusMessage(`已将 ${selectedTasks.size} 个任务分配到分组: ${groupName}`, 'success');
            }
        });
        
        // 删除选中任务
        deleteTasksBtn.addEventListener('click', function() {
            if (selectedTasks.size === 0) {
                showStatusMessage('请先选择要删除的任务', 'error');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${selectedTasks.size} 个任务吗？`)) {
                const taskIdsToDelete = Array.from(selectedTasks);
                
                // 从数组中删除任务
                for (let i = tasks.length - 1; i >= 0; i--) {
                    if (taskIdsToDelete.includes(tasks[i].id)) {
                        tasks.splice(i, 1);
                    }
                }
                
                selectedTasks.clear();
                selectedTaskId = null;
                updateTaskBtn.disabled = true;
                renderTaskList();
                renderGanttChart();
                showStatusMessage(`已删除 ${taskIdsToDelete.length} 个任务`, 'success');
            }
        });
        
        // 任务选择
        document.querySelectorAll('.task-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.task-item').forEach(task => {
                    task.classList.remove('selected');
                });
                this.classList.add('selected');
                
                // 更新选中任务ID
                selectedTaskId = parseInt(this.dataset.id) || 1;
                updateTaskBtn.disabled = false;
                
                // 更新表单数据
                const task = tasks.find(t => t.id === selectedTaskId);
                if (task) {
                    document.getElementById('taskName').value = task.name;
                    document.getElementById('taskGroup').value = task.group;
                    document.getElementById('taskStartDate').value = task.start;
                    document.getElementById('taskDuration').value = task.duration;
                    
                    // 设置颜色
                    document.querySelectorAll('.color-option').forEach(opt => {
                        opt.classList.remove('active');
                        if (opt.dataset.color === task.color) {
                            opt.classList.add('active');
                        }
                    });
                }
            });
        });
        
        // 添加任务
        addTaskBtn.addEventListener('click', function() {
            const name = document.getElementById('taskName').value;
            const group = document.getElementById('taskGroup').value;
            const start = document.getElementById('taskStartDate').value;
            const duration = parseInt(document.getElementById('taskDuration').value);
            const color = document.querySelector('.color-option.active').dataset.color;
            
            if (!name || !start || duration <= 0) {
                showStatusMessage('请填写所有必填字段', 'error');
                return;
            }
            
            // 创建新任务
            const newTask = {
                id: tasks.length + 1,
                name,
                group,
                start,
                duration,
                color
            };
            
            tasks.push(newTask);
            renderTaskList();
            renderGanttChart();
            showStatusMessage('任务已成功添加！', 'success');
        });
        
        // 更新任务
        updateTaskBtn.addEventListener('click', function() {
            const name = document.getElementById('taskName').value;
            const group = document.getElementById('taskGroup').value;
            const start = document.getElementById('taskStartDate').value;
            const duration = parseInt(document.getElementById('taskDuration').value);
            const color = document.querySelector('.color-option.active').dataset.color;
            
            if (!name || !start || duration <= 0) {
                showStatusMessage('请填写所有必填字段', 'error');
                return;
            }
            
            // 更新任务
            const taskIndex = tasks.findIndex(t => t.id === selectedTaskId);
            if (taskIndex !== -1) {
                tasks[taskIndex] = {
                    ...tasks[taskIndex],
                    name,
                    group,
                    start,
                    duration,
                    color
                };
                
                renderTaskList();
                renderGanttChart();
                showStatusMessage('任务已成功更新！', 'success');
            }
        });
        
        // 保存项目
        saveProjectBtn.addEventListener('click', function() {
            showLoader();
            setTimeout(() => {
                hideLoader();
                showStatusMessage('项目已成功保存！', 'success');
            }, 1500);
        });
        
        // 导入Excel
        importBtn.addEventListener('click', function() {
            importModal.style.display = 'flex';
        });
        
        // 关闭模态框
        document.querySelectorAll('.close, #cancelImportBtn').forEach(btn => {
            btn.addEventListener('click', function() {
                importModal.style.display = 'none';
            });
        });
        
        // 确认导入
        document.getElementById('confirmImportBtn').addEventListener('click', function() {
            const fileInput = document.getElementById('excelFile');
            if (fileInput.files.length === 0) {
                showStatusMessage('请选择Excel文件', 'error');
                return;
            }
            
            showLoader();
            setTimeout(() => {
                hideLoader();
                importModal.style.display = 'none';
                showStatusMessage('数据导入成功！', 'success');
            }, 2000);
        });
        
        // 导出Excel
        exportBtn.addEventListener('click', function() {
            showLoader();
            setTimeout(() => {
                // 创建Excel工作簿
                const wb = XLSX.utils.book_new();
                
                // 准备数据
                const exportData = tasks.map(task => ({
                    '任务名称': task.name,
                    '任务分组': task.group,
                    '开始日期': task.start,
                    '持续时间(天)': task.duration,
                    '结束日期': calculateEndDate(task.start, task.duration),
                    '颜色': task.color
                }));
                
                // 创建工作表
                const ws = XLSX.utils.json_to_sheet(exportData);
                
                // 将工作表添加到工作簿
                XLSX.utils.book_append_sheet(wb, ws, "施工任务");
                
                // 导出Excel文件
                XLSX.writeFile(wb, "施工进度计划.xlsx");
                
                hideLoader();
                showStatusMessage('Excel导出成功！', 'success');
            }, 1000);
        });
        
        // 保存到本地存储
        document.getElementById('saveLocalBtn').addEventListener('click', function() {
            localStorage.setItem('ganttTasks', JSON.stringify(tasks));
            showStatusMessage('数据已保存到本地存储！', 'success');
        });
        
        // 从本地存储加载
        document.getElementById('loadLocalBtn').addEventListener('click', function() {
            const savedTasks = localStorage.getItem('ganttTasks');
            if (savedTasks) {
                tasks.length = 0;
                tasks.push(...JSON.parse(savedTasks));
                renderTaskList();
                renderGanttChart();
                showStatusMessage('数据已从本地存储加载！', 'success');
            } else {
                showStatusMessage('未找到保存的数据', 'error');
            }
        });
        
        // 渲染任务列表
        function renderTaskList() {
            taskList.innerHTML = '';
            
            // 根据筛选条件过滤任务
            const filteredTasks = currentFilter === 'all' ? tasks : tasks.filter(task => task.group === currentFilter);
            
            // 按分组分组显示
            const groupedTasks = {};
            filteredTasks.forEach(task => {
                if (!groupedTasks[task.group]) {
                    groupedTasks[task.group] = [];
                }
                groupedTasks[task.group].push(task);
            });
            
            // 渲染每个分组的任务
            Object.keys(groupedTasks).forEach(groupName => {
                // 添加分组标题
                if (currentFilter === 'all') {
                    const groupHeader = document.createElement('div');
                    groupHeader.className = 'group-header';
                    groupHeader.innerHTML = `
                        <div style="background-color: var(--primary-color); color: white; padding: 10px 15px; font-weight: 600; margin: 10px 0 5px 0; border-radius: 4px;">
                            <i class="fas fa-folder"></i> ${groupName} (${groupedTasks[groupName].length}个任务)
                        </div>
                    `;
                    taskList.appendChild(groupHeader);
                }
                
                // 渲染该分组的所有任务
                groupedTasks[groupName].forEach(task => {
                    const endDate = calculateEndDate(task.start, task.duration);
                    const taskItem = document.createElement('div');
                    taskItem.className = `task-item ${task.id === selectedTaskId ? 'selected' : ''}`;
                    taskItem.dataset.id = task.id;
                    
                    taskItem.innerHTML = `
                        <div class="task-checkbox">
                            <input type="checkbox" data-task-id="${task.id}" ${selectedTasks.has(task.id) ? 'checked' : ''}>
                        </div>
                        <div class="task-color" style="background-color: ${task.color};"></div>
                        <div class="task-info">
                            <div class="task-name">${task.name}</div>
                            <div class="task-dates">${task.start} 至 ${endDate} (${task.duration}天)</div>
                        </div>
                        <div class="task-actions">
                            <button class="edit-task" data-task-id="${task.id}"><i class="fas fa-edit"></i></button>
                            <button class="delete-task" data-task-id="${task.id}"><i class="fas fa-trash"></i></button>
                        </div>
                    `;
                    
                    taskList.appendChild(taskItem);
                });
            });
            
            // 绑定复选框事件
            document.querySelectorAll('.task-checkbox input').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const taskId = parseInt(this.dataset.taskId);
                    if (this.checked) {
                        selectedTasks.add(taskId);
                    } else {
                        selectedTasks.delete(taskId);
                    }
                });
            });
            
            // 绑定编辑和删除按钮事件
            document.querySelectorAll('.edit-task').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const taskId = parseInt(this.dataset.taskId);
                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        // 模拟点击任务项来选中并编辑
                        const taskItem = document.querySelector(`[data-id="${taskId}"]`);
                        if (taskItem) {
                            taskItem.click();
                        }
                    }
                });
            });
            
            document.querySelectorAll('.delete-task').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const taskId = parseInt(this.dataset.taskId);
                    if (confirm('确定要删除这个任务吗？')) {
                        const taskIndex = tasks.findIndex(t => t.id === taskId);
                        if (taskIndex !== -1) {
                            tasks.splice(taskIndex, 1);
                            selectedTasks.delete(taskId);
                            if (selectedTaskId === taskId) {
                                selectedTaskId = null;
                                updateTaskBtn.disabled = true;
                            }
                            renderTaskList();
                            renderGanttChart();
                            showStatusMessage('任务已删除', 'success');
                        }
                    }
                });
            });
            
            // 绑定任务点击事件
            document.querySelectorAll('.task-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的是复选框或按钮，不触发任务选择
                    if (e.target.closest('.task-checkbox') || e.target.closest('.task-actions')) {
                        return;
                    }
                    
                    document.querySelectorAll('.task-item').forEach(task => {
                        task.classList.remove('selected');
                    });
                    this.classList.add('selected');
                    selectedTaskId = parseInt(this.dataset.id);
                    updateTaskBtn.disabled = false;
                    
                    // 更新表单
                    const task = tasks.find(t => t.id === selectedTaskId);
                    if (task) {
                        document.getElementById('taskName').value = task.name;
                        document.getElementById('taskGroup').value = task.group;
                        document.getElementById('taskStartDate').value = task.start;
                        document.getElementById('taskDuration').value = task.duration;
                        
                        document.querySelectorAll('.color-option').forEach(opt => {
                            opt.classList.remove('active');
                            if (opt.dataset.color === task.color) {
                                opt.classList.add('active');
                            }
                        });
                    }
                });
            });
        }
        
        // 渲染甘特图
        function renderGanttChart() {
            const ganttChart = document.querySelector('.gantt-chart');
            const timeline = document.querySelector('.timeline-months');
            
            if (!ganttChart || !timeline) return;
            
            // 清空现有内容
            ganttChart.innerHTML = '';
            timeline.innerHTML = '';
            
            // 计算日期范围
            let minDate = new Date();
            let maxDate = new Date();
            
            tasks.forEach(task => {
                const taskStart = new Date(task.start);
                const taskEnd = new Date(calculateEndDate(task.start, task.duration));
                
                if (taskStart < minDate) minDate = taskStart;
                if (taskEnd > maxDate) maxDate = taskEnd;
            });
            
            // 扩展范围以确保有足够空间
            minDate.setDate(minDate.getDate() - 7);
            maxDate.setDate(maxDate.getDate() + 7);
            
            const totalDays = Math.ceil((maxDate - minDate) / (1000 * 60 * 60 * 24));
            
            // 获取当前时间刻度
            const timeScale = document.getElementById('timeScale').value;
            const dayWidth = timeScale === 'day' ? 30 : timeScale === 'week' ? 40 : 20;
            
            // 生成时间轴
            generateTimeline(timeline, minDate, maxDate, timeScale, dayWidth);
            
            // 渲染任务条
            tasks.forEach(task => {
                const taskRow = document.createElement('div');
                taskRow.className = 'task-row';
                
                const taskLabel = document.createElement('div');
                taskLabel.className = 'task-label';
                taskLabel.textContent = task.name;
                
                const taskBarContainer = document.createElement('div');
                taskBarContainer.className = 'task-bar-container';
                taskBarContainer.style.width = `${totalDays * dayWidth}px`;
                
                const taskStart = new Date(task.start);
                const daysFromStart = Math.ceil((taskStart - minDate) / (1000 * 60 * 60 * 24));
                const leftPosition = daysFromStart * dayWidth;
                const barWidth = task.duration * dayWidth;
                
                const taskBar = document.createElement('div');
                taskBar.className = 'task-bar';
                taskBar.style.backgroundColor = task.color;
                taskBar.style.left = `${leftPosition}px`;
                taskBar.style.width = `${barWidth}px`;
                taskBar.textContent = task.name;
                taskBar.title = `${task.name} (${task.duration}天)`;
                taskBar.dataset.taskId = task.id;
                
                // 添加调整大小手柄
                const leftHandle = document.createElement('div');
                leftHandle.className = 'resize-handle left';
                leftHandle.dataset.taskId = task.id;
                leftHandle.dataset.handleType = 'left';
                
                const rightHandle = document.createElement('div');
                rightHandle.className = 'resize-handle right';
                rightHandle.dataset.taskId = task.id;
                rightHandle.dataset.handleType = 'right';
                
                taskBar.appendChild(leftHandle);
                taskBar.appendChild(rightHandle);
                
                // 绑定拖拽事件
                taskBar.addEventListener('mousedown', handleTaskBarMouseDown);
                leftHandle.addEventListener('mousedown', handleResizeMouseDown);
                rightHandle.addEventListener('mousedown', handleResizeMouseDown);
                
                taskBarContainer.appendChild(taskBar);
                taskRow.appendChild(taskLabel);
                taskRow.appendChild(taskBarContainer);
                ganttChart.appendChild(taskRow);
            });
        }
        
        // 生成时间轴
        function generateTimeline(timeline, minDate, maxDate, timeScale, dayWidth) {
            const currentDate = new Date(minDate);
            currentDate.setDate(1); // 从月的第一天开始
            
            while (currentDate <= maxDate) {
                const monthDiv = document.createElement('div');
                monthDiv.className = 'timeline-month';
                
                const monthName = document.createElement('div');
                monthName.className = 'month-name';
                monthName.textContent = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`;
                
                const daysDiv = document.createElement('div');
                daysDiv.className = 'month-days';
                
                const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();
                const startDay = timeScale === 'day' ? 1 : timeScale === 'week' ? 1 : 1;
                const step = timeScale === 'day' ? 1 : timeScale === 'week' ? 7 : 1;
                
                for (let day = startDay; day <= daysInMonth; day += step) {
                    const dayDiv = document.createElement('div');
                    dayDiv.className = 'month-day';
                    dayDiv.textContent = day;
                    dayDiv.style.width = `${dayWidth * step}px`;
                    daysDiv.appendChild(dayDiv);
                }
                
                monthDiv.appendChild(monthName);
                monthDiv.appendChild(daysDiv);
                timeline.appendChild(monthDiv);
                
                currentDate.setMonth(currentDate.getMonth() + 1);
            }
        }
        
        // 计算结束日期
        function calculateEndDate(startDate, duration) {
            const date = new Date(startDate);
            date.setDate(date.getDate() + duration);
            return date.toISOString().split('T')[0];
        }
        
        // 显示状态消息
        function showStatusMessage(message, type) {
            const icon = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';
            statusMessage.innerHTML = `<i class="${icon}"></i> <span>${message}</span>`;
            statusMessage.style.backgroundColor = type === 'error' ? 'var(--accent-color)' : 'var(--success-color)';
            statusMessage.classList.add('show');
            
            setTimeout(() => {
                statusMessage.classList.remove('show');
            }, 3000);
        }
        
        // 显示加载指示器
        function showLoader() {
            loader.style.display = 'flex';
        }
        
        // 隐藏加载指示器
        function hideLoader() {
            loader.style.display = 'none';
        }
        
        // 时间刻度变化事件
        document.getElementById('timeScale').addEventListener('change', function() {
            renderGanttChart();
        });
        
        // 列设置按钮
        columnSettingsBtn.addEventListener('click', function() {
            columnModal.style.display = 'flex';
        });
        
        // 图表设置按钮
        chartSettingsBtn.addEventListener('click', function() {
            styleModal.style.display = 'flex';
        });
        
        // 关闭列设置模态框
        document.querySelectorAll('#columnModal .close, #cancelColumnBtn').forEach(btn => {
            btn.addEventListener('click', function() {
                columnModal.style.display = 'none';
            });
        });
        
        // 关闭样式设置模态框
        document.querySelectorAll('#styleModal .close, #cancelStyleBtn').forEach(btn => {
            btn.addEventListener('click', function() {
                styleModal.style.display = 'none';
            });
        });
        
        // 样式滑块值更新
        taskBarHeight.addEventListener('input', function() {
            taskBarHeightValue.textContent = `${this.value}px`;
        });
        
        taskBarOpacity.addEventListener('input', function() {
            taskBarOpacityValue.textContent = `${Math.round(this.value * 100)}%`;
        });
        
        taskBarBorderWidth.addEventListener('input', function() {
            taskBarBorderWidthValue.textContent = `${this.value}px`;
        });
        
        taskBarBorderRadius.addEventListener('input', function() {
            taskBarBorderRadiusValue.textContent = `${this.value}px`;
        });
        
        // 应用样式按钮
        document.getElementById('applyStyleBtn').addEventListener('click', function() {
            const height = taskBarHeight.value;
            const opacity = taskBarOpacity.value;
            const borderWidth = taskBarBorderWidth.value;
            const borderColor = taskBarBorderColor.value;
            const borderRadius = taskBarBorderRadius.value;
            
            // 创建自定义样式
            const customStyles = document.createElement('style');
            customStyles.textContent = `
                .task-bar {
                    height: ${height}px !important;
                    opacity: ${opacity} !important;
                    border: ${borderWidth}px solid ${borderColor} !important;
                    border-radius: ${borderRadius}px !important;
                }
            `;
            
            // 移除之前的自定义样式
            const existingStyles = document.querySelector('#custom-task-styles');
            if (existingStyles) {
                existingStyles.remove();
            }
            
            customStyles.id = 'custom-task-styles';
            document.head.appendChild(customStyles);
            
            styleModal.style.display = 'none';
            showStatusMessage('样式已应用到所有任务条', 'success');
        });
        
        // 列设置滑块值更新
        taskLabelWidth.addEventListener('input', function() {
            taskLabelWidthValue.textContent = `${this.value}px`;
        });
        
        leftPanelWidth.addEventListener('input', function() {
            leftPanelWidthValue.textContent = `${this.value}px`;
        });
        
        // 应用列设置按钮
        document.getElementById('applyColumnBtn').addEventListener('click', function() {
            const showCheckboxChecked = showCheckbox.checked;
            const showColorChecked = showColor.checked;
            const showNameChecked = showName.checked;
            const showDatesChecked = showDates.checked;
            const showActionsChecked = showActions.checked;
            const labelWidth = taskLabelWidth.value;
            const panelWidth = leftPanelWidth.value;
            
            // 创建自定义列显示样式
            const columnStyles = document.createElement('style');
            columnStyles.textContent = `
                .task-checkbox { display: ${showCheckboxChecked ? 'block' : 'none'} !important; }
                .task-color { display: ${showColorChecked ? 'block' : 'none'} !important; }
                .task-name { display: ${showNameChecked ? 'block' : 'none'} !important; }
                .task-dates { display: ${showDatesChecked ? 'block' : 'none'} !important; }
                .task-actions { display: ${showActionsChecked ? 'flex' : 'none'} !important; }
                .task-label { min-width: ${labelWidth}px !important; }
                .panel-left { flex: 0 0 ${panelWidth}px !important; }
            `;
            
            // 移除之前的列样式
            const existingColumnStyles = document.querySelector('#custom-column-styles');
            if (existingColumnStyles) {
                existingColumnStyles.remove();
            }
            
            columnStyles.id = 'custom-column-styles';
            document.head.appendChild(columnStyles);
            
            columnModal.style.display = 'none';
            showStatusMessage('列设置已应用', 'success');
        });
        
        // 初始化日期输入为今天
        document.getElementById('taskStartDate').valueAsDate = new Date();
        
        // 拖拽任务条鼠标按下事件
        function handleTaskBarMouseDown(e) {
            if (e.target.classList.contains('resize-handle')) return;
            
            e.preventDefault();
            isDragging = true;
            currentDragElement = e.currentTarget;
            currentTaskId = parseInt(e.currentTarget.dataset.taskId);
            dragStartX = e.clientX;
            dragStartLeft = parseInt(currentDragElement.style.left);
            
            currentDragElement.classList.add('dragging');
            document.addEventListener('mousemove', handleTaskBarMouseMove);
            document.addEventListener('mouseup', handleTaskBarMouseUp);
        }
        
        // 调整大小鼠标按下事件
        function handleResizeMouseDown(e) {
            e.preventDefault();
            e.stopPropagation();
            isResizing = true;
            currentDragElement = e.currentTarget.parentElement;
            currentTaskId = parseInt(e.currentTarget.dataset.taskId);
            const handleType = e.currentTarget.dataset.handleType;
            
            dragStartX = e.clientX;
            dragStartLeft = parseInt(currentDragElement.style.left);
            dragStartWidth = parseInt(currentDragElement.style.width);
            
            document.addEventListener('mousemove', (e) => handleResizeMouseMove(e, handleType));
            document.addEventListener('mouseup', handleResizeMouseUp);
        }
        
        // 拖拽任务条鼠标移动事件
        function handleTaskBarMouseMove(e) {
            if (!isDragging) return;
            
            const deltaX = e.clientX - dragStartX;
            const newLeft = Math.max(0, dragStartLeft + deltaX);
            currentDragElement.style.left = `${newLeft}px`;
        }
        
        // 调整大小鼠标移动事件
        function handleResizeMouseMove(e, handleType) {
            if (!isResizing) return;
            
            const deltaX = e.clientX - dragStartX;
            
            if (handleType === 'left') {
                const newLeft = Math.max(0, dragStartLeft + deltaX);
                const newWidth = Math.max(20, dragStartWidth - deltaX);
                currentDragElement.style.left = `${newLeft}px`;
                currentDragElement.style.width = `${newWidth}px`;
            } else {
                const newWidth = Math.max(20, dragStartWidth + deltaX);
                currentDragElement.style.width = `${newWidth}px`;
            }
        }
        
        // 拖拽任务条鼠标释放事件
        function handleTaskBarMouseUp(e) {
            if (!isDragging) return;
            
            isDragging = false;
            currentDragElement.classList.remove('dragging');
            
            // 计算新的开始日期
            const timeScale = document.getElementById('timeScale').value;
            const dayWidth = timeScale === 'day' ? 30 : timeScale === 'week' ? 40 : 20;
            const newLeft = parseInt(currentDragElement.style.left);
            const daysMoved = Math.round(newLeft / dayWidth);
            
            // 更新任务数据
            const task = tasks.find(t => t.id === currentTaskId);
            if (task) {
                const newStartDate = new Date(task.start);
                newStartDate.setDate(newStartDate.getDate() + daysMoved);
                task.start = newStartDate.toISOString().split('T')[0];
                
                // 更新任务列表和显示
                renderTaskList();
                renderGanttChart();
                showStatusMessage(`任务 "${task.name}" 已移动到 ${task.start}`, 'success');
            }
            
            document.removeEventListener('mousemove', handleTaskBarMouseMove);
            document.removeEventListener('mouseup', handleTaskBarMouseUp);
        }
        
        // 调整大小鼠标释放事件
        function handleResizeMouseUp(e) {
            if (!isResizing) return;
            
            isResizing = false;
            
            // 计算新的持续时间
            const timeScale = document.getElementById('timeScale').value;
            const dayWidth = timeScale === 'day' ? 30 : timeScale === 'week' ? 40 : 20;
            const newWidth = parseInt(currentDragElement.style.width);
            const newDuration = Math.max(1, Math.round(newWidth / dayWidth));
            
            // 更新任务数据
            const task = tasks.find(t => t.id === currentTaskId);
            if (task) {
                // 如果是左侧拖拽，还需要更新开始时间
                const newLeft = parseInt(currentDragElement.style.left);
                const oldLeft = dragStartLeft;
                const daysMoved = Math.round((newLeft - oldLeft) / dayWidth);
                
                if (daysMoved !== 0) {
                    const newStartDate = new Date(task.start);
                    newStartDate.setDate(newStartDate.getDate() + daysMoved);
                    task.start = newStartDate.toISOString().split('T')[0];
                }
                
                task.duration = newDuration;
                
                // 更新任务列表和显示
                renderTaskList();
                renderGanttChart();
                showStatusMessage(`任务 "${task.name}" 持续时间已更新为 ${newDuration} 天`, 'success');
            }
            
            document.removeEventListener('mousemove', handleResizeMouseMove);
            document.removeEventListener('mouseup', handleResizeMouseUp);
        }
        
        // 初始渲染
        renderTaskList();
        renderGanttChart();
    </script>
</body>
</html>